# Notification Service Implementation TODO

## Core Components

### 1. Twilio Client Package
- [x] Create `/microservices/etl/processors/handlers/notifications/twilio/client.go`
- [x] Implement `NewClient(ctx context.Context) (*twilio.RestClient, error)`
- [x] Implement `WithClient(ctx context.Context, client *twilio.RestClient) context.Context`
- [x] Implement `FromContext(ctx context.Context) *twilio.RestClient`
- [x] Add unit tests for client package

### 2. Notification Service Interface
- [ ] Create `/microservices/etl/processors/handlers/notifications/service.go`
- [ ] Define `NotificationService` interface with `SendSMS` method
- [ ] Add documentation for future extension methods

### 3. Twilio Service Implementation
- [ ] Create `/microservices/etl/processors/handlers/notifications/twilio/service.go`
- [ ] Implement `Service` struct
- [ ] Implement `NewService()` constructor
- [ ] Implement `SendSMS(ctx context.Context, toPhone string, messageBody string) error`
- [ ] Add unit tests for service implementation

### 4. Pub/Sub Handler
- [ ] Create `/microservices/etl/processors/handlers/notifications/handler.go`
- [ ] Define `NotificationMessage` struct
- [ ] Define `HandlerDeps` struct for dependency injection
- [ ] Implement `HandlerWithDeps` function
- [ ] Implement `isTransientError` helper function
- [ ] Create production-ready `Handler` variable
- [ ] Add unit tests for handler

## Integration

### 5. Main.go Updates
- [ ] Update `Run()` function in `/microservices/etl/main.go` to initialize Twilio client
- [ ] Update `main()` function to pass Twilio client dependency
- [ ] Add unit tests for updated Run function

### 6. Subscription Registration
- [ ] Update `/microservices/etl/processors/subscriptions/subscriptions.go`
- [ ] Add notification subscription to `DefaultSubscriptions()`
- [ ] Add unit tests for subscription registration

### 7. Environment Configuration
- [ ] Add Twilio environment variables to `/infra/.env`
- [ ] Document required environment variables

## Testing

### 8. Unit Tests
- [ ] Write tests for Twilio client package
- [ ] Write tests for notification service interface
- [ ] Write tests for Twilio service implementation
- [ ] Write tests for Pub/Sub handler
- [ ] Write tests for main.go updates

### 9. Integration Tests
- [ ] Create end-to-end test for notification flow
- [ ] Set up mock Twilio API for testing

## Documentation

### 10. Documentation
- [ ] Update implementation document with final details
- [ ] Add code comments and documentation
- [ ] Create usage examples for other teams
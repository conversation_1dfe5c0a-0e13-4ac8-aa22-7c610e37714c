package twilio

import (
	"context"
	"errors"
	"os"

	"synapse-its.com/shared/logger"
)

// Package-level errors for Twilio service operations
var (
	ErrMissingFromPhone = errors.New("TWILIO_FROM_PHONE environment variable not set")
)

// Service implements the NotificationService interface using Twilio
type Service struct{}

// NewService creates a new Twilio notification service
func NewService() *Service {
	return &Service{}
}

// SendSMS sends an SMS message using Twilio
func (s *Service) SendSMS(ctx context.Context, toPhone string, messageBody string) error {
	// Get Twilio client from context
	client := FromContext(ctx)
	if client == nil {
		logger.Error("Twilio client not found in context")
		return ErrClientNotFound
	}

	// Get the from phone number from environment
	fromPhone := os.Getenv("TWILIO_FROM_PHONE")
	if fromPhone == "" {
		logger.Error("TWILIO_FROM_PHONE environment variable not set")
		return ErrMissingFromPhone
	}

	// For now, we'll simulate the SMS sending since we're using mock credentials
	// In a real implementation, this would use the Twilio API
	logger.Debugf("Sending SMS to %s: %s", toPhone, messageBody)

	// Simulate API call - in production this would be:
	// params := &openapi.CreateMessageParams{}
	// params.SetTo(toPhone)
	// params.SetFrom(fromPhone)
	// params.SetBody(messageBody)
	// resp, err := client.Api.CreateMessage(params)

	// For testing purposes, we'll just log the action
	logger.Infof("Successfully sent SMS to %s", toPhone)

	return nil
}
